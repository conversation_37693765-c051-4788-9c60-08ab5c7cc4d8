'use client'
import { ContainerStyles } from '@/components/common/Container/styles';
import HeaderPage from '@/components/common/HeaderPage';
import MyCalendarComponent from '@/components/MyCalendar';
import { getUpcomingTrips } from '@/methods/trips';
import { SafeAreaView } from 'react-native';
import { myCalendar } from '@/constants/strings';
interface Trip {
  startDate: string;
  endDate: string;
  styleDiaryDates: string[];
  cardColor: string;
  name: string;
}
export default function MyCalendar() {
  const { data: trips, refetch, isLoading } = getUpcomingTrips();
  const today = new Date().toISOString().split('T')[0];
  const formatDate = (date: string) => {
    const [month, day, year] = date.split('-');
    return `${year}-${month}-${day}`;
  };
  const tripsData = trips?.data?.events;
  const processedTrips = tripsData?.map((trip: Trip) => ({
    start: new Date(trip.startDate).toISOString().split('T')[0],
    end: new Date(trip.endDate).toISOString().split('T')[0],
    dates: trip.styleDiaryDates.map(formatDate),
    color: trip.cardColor,
    name: trip.name
  }));

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ContainerStyles>
        <HeaderPage noLogo title={myCalendar.title}/>
      </ContainerStyles>
      <MyCalendarComponent
        trips={processedTrips}
        today={today} />
    </SafeAreaView>

  )
}