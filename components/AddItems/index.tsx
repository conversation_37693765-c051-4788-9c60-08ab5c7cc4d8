import {
  ActivityIndicator,
  FlatList,
  KeyboardAvoidingView,
  LayoutChangeEvent,
  TouchableOpacity,
  View,
  Image,
  Alert,
  ScrollView,
} from "react-native";
import useResponsive from "@/hooks/useResponsive";
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CheckBox,
  CategoryItem,
  CategoryItemText,
  HeaderTitle,
  CategoryItemContainer,
  CategoryItemTextButton,
  QuantityButton,
  ClothsImageContainer,
} from "./styles";
import Button from "@/components/common/Button";
import Modal from "react-native-modal";
import { XIcon, PlusIcon, CheckIcon, MinusIcon } from "lucide-react-native";
import Input from "../common/Input";
import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { CLOTHES, CLOTHES_CATEGORIES } from "@/constants/Clothes";
import { SubtitleText, TitleSection } from "../common/Text";
import { getCategories, addItem, ItemsUpdate } from "@/methods/cloths";
import * as ImagePicker from "expo-image-picker";
import { Image as ImageComponent } from "react-native";
import { UploadImage, uploadImageToS3 } from "@/methods/cloths";
// Import API-based categories
import { fetchCategoriesFromBackend } from "@/data/gender-categories";
import { getCurrentUserGender } from "@/data/categories";
import { getUserProfile } from "@/methods/users";
import { capitalizeWords } from "@/utils/capitalizeWords";

interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  item: any;
  selectedItem: any;
  packingList: any[];
  setIsModalVisible: (isVisible: boolean) => void;
  saveEditCloths: (
    note: string,
    quantity: number,
    parentId: string,
    itemId: string
  ) => void;
  removeItem: (selectedItem: any, item: any) => void;
  handleAddItemInPackingList: (item: any) => void;
}

export default function AddItemsModal({
  isVisible,
  onClose,
  item,
  selectedItem,
  packingList = [],
  saveEditCloths,
  removeItem,
  setIsModalVisible,
  handleAddItemInPackingList,
}: AddClothsCategoryModalProps) {
  // Initialize queryClient for cache invalidation
  const queryClient = useQueryClient();

  // Get responsive values
  const { isTablet, padding } = useResponsive();

  const [width, setWidth] = useState(0);
  const [quantity, setQuantity] = useState(item?.quantity || 1);
  const [selectedCategory, setSelectedCategory] = useState(
    item?.category || ""
  );
  const { data: categories } = getCategories();
  const {
    mutate: addItemMutation,
    isPending: isAddingItem,
    isSuccess: isItemAdded,
    data: itemData,
  } = addItem();
  const {
    mutate: uploadImageMutation,
    isPending: isUploadingImage,
    isSuccess: isImageUploaded,
    data: imageData,
  } = UploadImage();
  const {
    mutate: uploadImageToS3Mutation,
    isPending: isUploadingImageToS3,
    isSuccess: isImageUploadedToS3,
    data: imageDataToS3,
  } = uploadImageToS3();
  const {
    mutate: updateItemMutation,
    isPending: isUpdatingItem,
    isSuccess: isItemUpdated,
    data: itemUpdatedData,
  } = ItemsUpdate();

  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>([]);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  const [itemName, setItemName] = useState("");
  const [color, setColor] = useState("");
  const [size, setSize] = useState("");
  const [material, setMaterial] = useState("");
  const [image, setImage] = useState("");
  const [displayImage, setDisplayImage] = useState("");
  const [note, setNote] = useState("");
  const [brand, setBrand] = useState("");

  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: false,
      base64: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].base64 || "");
      setDisplayImage(result.assets[0].uri);
    }
  };

  useEffect(() => {
    if (imageDataToS3 && itemData?.itemId) {
      updateItemMutation(
        {
          itemId: itemData?.itemId,
          imageUrl: imageData?.fileURL,
        },
        {
          onSuccess: (response) => {
            console.log("imageDataToS3@@@@@", response);
          },
        }
      );
    }
  }, [isImageUploadedToS3]);

  useEffect(() => {
    if (isItemUpdated) {
      // Make sure we have a valid itemId
      if (!itemData?.itemId) {
        Alert.alert(
          "Error",
          "Failed to update item properly. Please try again."
        );
        return;
      }

      const item = {
        _id: itemData?.itemId,
        name: itemName,
        category: selectedCategory,
        color: color,
        brand: brand,
        quantity: quantity,
        imageUrl: imageData?.fileURL,
      };

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["clothes"] });
      queryClient.invalidateQueries({ queryKey: ["item"] });
      queryClient.invalidateQueries({ queryKey: ["categories"] });

      // Force a refresh of the clothes data
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["clothes"] });
      }, 500);

      // Add the item to the packing list
      handleAddItemInPackingList([item]);

      // Close the modal and reset the image
      onClose();
      setDisplayImage("");

      // Show success message
      Alert.alert("Success", "Item added successfully");
    }
  }, [isItemUpdated]);

  useEffect(() => {
    if (isImageUploaded) {
      const preSignedUrl = imageData?.preSignedURL;
      const fileType = imageData?.fileType;

      uploadImageToS3Mutation({
        imageUrl: image,
        preSignedUrl: preSignedUrl,
      });
    }
  }, [isImageUploaded]);

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  useEffect(() => {
    if (isItemAdded) {
      if (image) {
        uploadImageMutation({
          fileName: itemName,
          fileType: "image/jpeg",
          folderPath: "items",
          imageUrl: image,
        });
      } else {
        // Make sure we have a valid itemId
        if (!itemData?.itemId) {
          Alert.alert(
            "Error",
            "Failed to add item properly. Please try again."
          );
          return;
        }

        const item = {
          _id: itemData?.itemId,
          name: itemName,
          category: selectedCategory,
          color: color,
          brand: brand,
          quantity: quantity,
          imageUrl: "",
        };

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ["clothes"] });
        queryClient.invalidateQueries({ queryKey: ["item"] });
        queryClient.invalidateQueries({ queryKey: ["categories"] });

        // Force a refresh of the clothes data
        setTimeout(() => {
          queryClient.refetchQueries({ queryKey: ["clothes"] });
        }, 500);

        // Add the item to the packing list
        handleAddItemInPackingList([item]);

        // Close the modal
        onClose();
      }
    }
  }, [isItemAdded]);

  useEffect(() => {
    setItemName("");
    setSelectedCategory("");
    setQuantity(1);
    setColor("");
    setSize("");
    setBrand("");
  }, [isVisible]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log("AddItems - Using gender from userProfile:", gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log(
            "AddItems - Using gender from getCurrentUserGender:",
            gender
          );
        }

        if (!gender) {
          console.warn(
            "AddItems - No gender available, using fallback categories"
          );
          // Use fallback categories
          const fallbackCategories = [
            { _id: "tops", name: "Tops", category: "Tops" },
            { _id: "bottoms", name: "Bottoms", category: "Bottoms" },
            { _id: "dresses", name: "Dresses", category: "Dresses" },
            { _id: "shoes", name: "Shoes", category: "Shoes" },
            {
              _id: "accessories",
              name: "Accessories",
              category: "Accessories",
            },
          ];
          setDisplayCategories(fallbackCategories);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        // Create a Map to track categories by name for deduplication
        const categoryMap = new Map<string, any>();

        // Collect categories by name, keeping only the first occurrence
        // Make sure backendCategories is an array before iterating
        if (!Array.isArray(backendCategories)) {
          console.error(
            "AddItems - backendCategories is not an array:",
            backendCategories
          );
          return; // Exit early if not an array
        }

        backendCategories.forEach((category) => {
          // Skip if category is undefined or null or has no name
          if (!category || !category.name) {
            console.log("AddItems - Skipping category with no name");
            return;
          }

          if (!categoryMap.has(category.name)) {
            categoryMap.set(category.name, category);
          } else {
            console.log(
              `AddItems - Skipping duplicate category: ${category.name}`
            );
          }
        });

        // Format categories for display - include all top-level categories (after deduplication)
        const formattedCategories = Array.from(categoryMap.values())
          .filter(
            (category) =>
              category !== null && category !== undefined && category.name
          ) // Filter out null/undefined and categories with no name
          .map((category) => ({
            _id:
              category.id ||
              `category-${category.name.toLowerCase().replace(/\s+/g, "-")}`,
            name: category.name,
            category: category.name,
          }));

        // Make sure we have all the basic categories at minimum
        const basicCategories = [
          "Tops",
          "Bottoms",
          "Dresses",
          "Shoes",
          "Accessories",
        ];

        // Check if each basic category exists in the formatted categories
        basicCategories.forEach((basicCat) => {
          if (!formattedCategories.some((cat) => cat.name === basicCat)) {
            // Add the missing basic category
            formattedCategories.push({
              _id: `basic-${basicCat.toLowerCase()}`,
              name: basicCat,
              category: basicCat,
            });
          }
        });

        console.log(
          `AddItems - Loaded ${
            formattedCategories.length
          } categories for gender: ${gender || "unknown"}`
        );
        setDisplayCategories(formattedCategories);
      } catch (error) {
        console.error("Error loading gender-specific categories:", error);
        // Fallback to a comprehensive set of categories if there's an error
        const fallbackCategories = [
          { _id: "tops", name: "Tops", category: "Tops" },
          { _id: "bottoms", name: "Bottoms", category: "Bottoms" },
          { _id: "dresses", name: "Dresses", category: "Dresses" },
          {
            _id: "matching-sets",
            name: "Matching Sets",
            category: "Matching Sets",
          },
          { _id: "outerwear", name: "Outerwear", category: "Outerwear" },
          { _id: "swimwear", name: "Swimwear", category: "Swimwear" },
          { _id: "activewear", name: "Activewear", category: "Activewear" },
          { _id: "shoes", name: "Shoes", category: "Shoes" },
          { _id: "jewellery", name: "Jewellery", category: "Jewellery" },
          { _id: "bags", name: "Bags", category: "Bags" },
          { _id: "accessories", name: "Accessories", category: "Accessories" },
          { _id: "tech", name: "Tech", category: "Tech" },
          { _id: "others", name: "Others", category: "Others" },
        ];
        setDisplayCategories(fallbackCategories);
      }
    };

    loadGenderCategories();
  }, [userProfile, categories]);

  const saveItem = () => {
    // Validate item name
    if (!itemName || itemName.trim() === "") {
      Alert.alert("Please enter an item name");
      return;
    }

    // Validate category
    if (!selectedCategory) {
      Alert.alert(
        "Please select a category",
        "Select a category in your Myuse closet to add this item"
      );
      return;
    }

    if (!selectedCategory._id) {
      Alert.alert("Error", "Invalid category selected. Please try again.");
      return;
    }

    // Prepare the data for the API call
    const itemData = {
      name: itemName.trim(),
      itemCategoryId: selectedCategory._id,
      color: color || "",
      brand: brand || "",
    };

    try {
      // Clear any existing clothes cache before adding the item
      queryClient.invalidateQueries({ queryKey: ["clothes"] });

      // Add the item
      addItemMutation(itemData);
    } catch (error) {
      Alert.alert("Error", "Failed to add item. Please try again.");
    }
  };

  return (
    <Modal
      key={item?.id}
      style={{ justifyContent: "flex-end", margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <KeyboardAvoidingView
        behavior="padding"
        style={{ flex: 1, justifyContent: "flex-end" }}
      >
        <AddClothsModalContainer>
          {isAddingItem ||
          isUploadingImage ||
          isUploadingImageToS3 ||
          isUpdatingItem ? (
            <ActivityIndicator size={400} color="#0E7E61" />
          ) : (
            <View>
              <AddClothsModalHeader>
                <TouchableOpacity style={{ flex: 1 }} onPress={onClose}>
                  <HeaderText>Cancel</HeaderText>
                </TouchableOpacity>
                <View style={{ flex: 3, alignItems: "center" }}>
                  <HeaderTitle>
                    Add New Item to Packing List: {item?.name}
                  </HeaderTitle>
                </View>
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  <TouchableOpacity
                    onPress={() => {
                      saveItem();
                    }}
                  >
                    <HeaderText>Save</HeaderText>
                  </TouchableOpacity>
                </View>
              </AddClothsModalHeader>
              <ScrollView
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                bounces={false}
                contentContainerStyle={{
                  paddingBottom: isTablet ? padding.MODAL_CONTENT_HORIZONTAL * 2 : padding.MODAL_CONTENT_HORIZONTAL * 1.5
                }}
              >
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={`Item Name / Description`} />
                  <AddClothsModalInput
                    placeholder="Item Name"
                    value={itemName}
                    onChangeText={setItemName}
                    onBlur={() => setItemName(capitalizeWords(itemName))}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={`Brand`} />
                  <AddClothsModalInput
                    placeholder="Brand name"
                    value={brand}
                    onChangeText={setBrand}
                    onBlur={() => setBrand(capitalizeWords(brand))}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={`Color`} />
                  <AddClothsModalInput
                    placeholder="Color"
                    value={color}
                    onChangeText={setColor}
                    onBlur={() => setColor(capitalizeWords(color))}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={`Myuse Closet Category:`} />
                  <FlatList
                    style={{ marginTop: 10 }}
                    data={displayCategories}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    renderItem={({ item }) => {
                      return (
                        <CategoryItem
                          key={item._id || item.id}
                          isSelected={selectedCategory.name === item.name}
                          onPress={() => setSelectedCategory(item)}
                        >
                          <CategoryItemText
                            isSelected={selectedCategory.name === item.name}
                          >
                            {item.name}
                          </CategoryItemText>
                        </CategoryItem>
                      );
                    }}
                  />
                </View>

                <View
                  style={{
                    justifyContent: "space-between",
                    marginTop: 20,
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 10,
                  }}
                >
                  <TitleSection string={`Quantity`} />
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 10,
                    }}
                  >
                    <QuantityButton
                      style={{ backgroundColor: "#B4D7CE" }}
                      onPress={() => setQuantity(quantity - 1)}
                    >
                      <MinusIcon color="#0E7E61" />
                    </QuantityButton>
                    <SubtitleText string={`${quantity}`} />
                    <QuantityButton
                      style={{ backgroundColor: "#0E7E61" }}
                      onPress={() => setQuantity(quantity + 1)}
                    >
                      <PlusIcon color="#FFF" />
                    </QuantityButton>
                  </View>
                </View>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={`Image`} />
                </View>
                <View style={{
                  marginTop: 20,
                  marginBottom: isTablet ? padding.MODAL_CONTENT_HORIZONTAL : padding.MODAL_CONTENT_HORIZONTAL * 0.75
                }}>
                  <ClothsImageContainer onPress={pickImage} width={width}>
                    {displayImage ? (
                      <ImageComponent
                        source={{ uri: displayImage }}
                        style={{ width: "100%", height: "100%" }}
                      />
                    ) : (
                      <PlusIcon color="#FFFF" />
                    )}
                  </ClothsImageContainer>
                </View>
                <ClothesContainer
                  onLayout={handleLayout}
                  style={{ marginTop: 20 }}
                ></ClothesContainer>
              </ScrollView>
            </View>
          )}
        </AddClothsModalContainer>
      </KeyboardAvoidingView>
    </Modal>
  );
}
