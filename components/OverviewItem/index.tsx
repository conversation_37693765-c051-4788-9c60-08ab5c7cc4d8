import { useEffect } from "react";
import {
  OverviewItemContainer,
  OverviewItemText,
  OverviewItemTextBold,
  ItemCountText,
  QuantityText,
  CircleIconContainer,
  BrandColorText,
} from "./styles";
import {
  PlusIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XIcon,
  RotateCcwIcon,
  CornerUpLeftIcon,
  CircleIcon,
} from "lucide-react-native";
import { TouchableOpacity, View } from "react-native";
import { ScaleDecorator } from "react-native-draggable-flatlist";
import { DragView, DropView, DragProvider } from "react-native-nested-drag";

interface OverviewItemProps {
  item: any;
  isActive: boolean;
  onPress: () => void;
  onLongPress: () => void;
  setShowSubItems: (id: string, show: boolean) => void;
  removeCategory: (category: any) => void;
  removeItem: (item: any) => void;
  removeSubItem: (parentId: string, subItemId: string) => void;
  isSubItem: boolean;
  parentId?: string;
  addCloths?: () => void;
  changeItemsActive?: (parentId: string, itemId: string) => void;
  changeAllActiveItems?: (parentId: string, isActive: boolean) => void;
}

export default function OverviewItem({
  item,
  isActive,
  onPress,
  onLongPress,
  setShowSubItems,
  removeCategory,
  removeItem,
  removeSubItem,
  isSubItem,
  parentId,
  addCloths,
  changeItemsActive,
  changeAllActiveItems,
}: OverviewItemProps) {
  const truncatedName =
    item?.name?.length > 40 ? item?.name?.substring(0, 40) + "..." : item?.name;

  if (item?.addItem) {
    return (
      <OverviewItemContainer onPress={addCloths}>
        <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
          <PlusIcon size={20} color="#5C5C5C" />
          <OverviewItemText
            style={{ color: "#5C5C5C" }}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            Add new item
          </OverviewItemText>
        </View>
      </OverviewItemContainer>
    );
  }

  useEffect(() => {
    console.log("Mounted OverviewItem", { id: item?._id, parentId });
  }, []);

  return (
    <OverviewItemContainer
      isItem={item?.type !== "category"}
      disabled={isActive}
      onPress={onPress}
      onLongPress={onLongPress}
    >
      <View style={{ flexDirection: "row", gap: 10 }}>
        {item?.isActive ? (
          <CircleIconContainer
            onPress={() => {
              if (isSubItem && changeItemsActive && parentId) {
                console.log(parentId, item._id, "parentId, item._id");
                changeItemsActive(parentId, item._id);
              } else {
                changeAllActiveItems(item._id, !item?.isActive);
              }
            }}
          >
            <CheckIcon size={15} color="#FFFFFF" />
          </CircleIconContainer>
        ) : (
          <CircleIcon
            onPress={() => {
              if (isSubItem && changeItemsActive && parentId) {
                console.log(parentId, item._id, "parentId, item._id");
                changeItemsActive(parentId, item._id);
              } else {
                changeAllActiveItems(item._id, !item?.isActive);
              }
            }}
            size={20}
            color="#0E7E61"
          />
        )}
        {item?.type === "category" ? (
          <OverviewItemTextBold numberOfLines={1} ellipsizeMode="tail">
            {truncatedName}
          </OverviewItemTextBold>
        ) : (
          <OverviewItemText numberOfLines={1} ellipsizeMode="tail">
            <View style={{ flexDirection: "column" }}>
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 10 }}
              >
                <QuantityText>{item?.quantity}x</QuantityText>
                <OverviewItemText numberOfLines={1} ellipsizeMode="tail">
                  {truncatedName}
                </OverviewItemText>
              </View>
              {!item?.brand && !item?.color && (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 10,
                  }}
                >
                  <BrandColorText>No Description</BrandColorText>
                </View>
              )}
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 10 }}
              >
                <>
                  {item?.brand && <BrandColorText>{item.brand}</BrandColorText>}
                  {item?.brand && item?.color && (
                    <BrandColorText>/</BrandColorText>
                  )}
                  {item?.color && <BrandColorText>{item.color}</BrandColorText>}
                </>
              </View>
            </View>
          </OverviewItemText>
        )}
        {/* add the branch name and color */}
      </View>
      {item?.type === "category" ? (
        <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
          <ItemCountText>
            ({item?.items?.filter((item: any) => item?.isActive).length} /{" "}
            {item?.items?.length})
          </ItemCountText>
          <TouchableOpacity
            onPress={() => {
              if (setShowSubItems && parentId) {
                console.log("Toggle pressed: parentId =", parentId);
                console.log("Previous showSubItems =", item?.showSubItems);
                setShowSubItems(parentId, !item?.showSubItems);
              }
            }}
          >
            {item?.showSubItems ? (
              <ChevronDownIcon size={20} color="black" />
            ) : (
              <ChevronUpIcon size={20} color="black" />
            )}
          </TouchableOpacity>

          <TouchableOpacity onPress={() => removeCategory(item)}>
            <XIcon size={20} color="black" />
          </TouchableOpacity>
        </View>
      ) : (
        <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
          <TouchableOpacity
            onPress={() => {
              isSubItem ? onPress() : removeItem(item._id);
            }}
          >
            <PlusIcon size={20} color="black" />
          </TouchableOpacity>
        </View>
      )}
    </OverviewItemContainer>
  );
}
